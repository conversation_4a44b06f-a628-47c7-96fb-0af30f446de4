package com.bxm.customer.domain.vo.valueAdded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 员工信息VO
 *
 * 用于DeliveryOrderVO中的员工信息列表，只包含业务表单中展示的必要字段
 *
 * <AUTHOR>
 * @date 2025-08-18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("员工信息VO")
public class EmployeeInfo {

    /** 方式（操作方式） */
    @ApiModelProperty(value = "方式")
    private Integer operationType;

    /** 姓名 */
    @ApiModelProperty(value = "姓名")
    private String employeeName;

    /** 身份证号 */
    @ApiModelProperty(value = "身份证号")
    private String idNumber;

    /** 手机号 */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 应发工资 */
    @ApiModelProperty(value = "应发工资")
    private BigDecimal grossSalary;

    /** 公积金个人缴存金额 */
    @ApiModelProperty(value = "公积金个人缴存金额")
    private BigDecimal providentFundPersonal;

    /** 申报基数（社保基数） */
    @ApiModelProperty(value = "申报基数")
    private BigDecimal socialInsuranceBase;

    /** 社保信息（养老、失业、工伤、医疗、生育） */
    @ApiModelProperty(value = "社保信息")
    private SocialInsuranceVO socialInsurance;
}
