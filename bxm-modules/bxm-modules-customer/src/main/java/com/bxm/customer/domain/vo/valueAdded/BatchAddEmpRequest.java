package com.bxm.customer.domain.vo.valueAdded;

import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.enums.ValueAddedEntryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 批量上传请求VO
 *
 * 用于批量上传社保员工信息的表单数据
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量上传请求VO")
public class BatchAddEmpRequest {

    @ApiModelProperty(value = "增值交付单编号，可选")
    private String deliveryOrderNo;

    /** 业务类型，1-社医保，2-个税明细 */
    @Min(value = 1, message = "业务类型必须为1-3之间的数字")
    @Max(value = 3, message = "业务类型必须为1-3之间的数字")
    @ApiModelProperty(value = "业务类型：1-社医保，2-个税明细，3-国税账号", allowableValues = "1,2,3")
    private Integer bizType = ValueAddedBizType.SOCIAL_INSURANCE.getCode(); // 默认为社医保

    /** 录入方式，1-批量新增，2-单个新增 */
    @ApiModelProperty(value = "录入方式：1-批量新增，2-单个新增", allowableValues = "1,2")
    private Integer entryType = ValueAddedEntryType.BATCH_ADD.getCode(); // 默认为批量新增

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 是否覆盖现有数据 */
    @ApiModelProperty(value = "是否覆盖现有数据，默认false")
    private Boolean overrideExisting = false;
}
