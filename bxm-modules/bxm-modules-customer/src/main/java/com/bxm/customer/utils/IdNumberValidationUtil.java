package com.bxm.customer.utils;

import com.bxm.common.core.utils.StringUtils;

/**
 * 身份证号验证工具类
 * 
 * 提供身份证号格式验证的通用方法，供各业务策略复用。
 * 支持18位和15位身份证号的格式验证。
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public class IdNumberValidationUtil {

    /**
     * 验证身份证号格式是否正确
     * 
     * @param idNumber 身份证号
     * @param allowEmpty 是否允许为空
     * @return 验证结果
     */
    public static boolean isValid(String idNumber, boolean allowEmpty) {
        // 如果允许为空且值为空，则验证通过
        if (allowEmpty && StringUtils.isEmpty(idNumber)) {
            return true;
        }

        // 如果不允许为空且值为空，则验证失败
        if (!allowEmpty && StringUtils.isEmpty(idNumber)) {
            return false;
        }

        // 去除空格
        idNumber = idNumber.trim();

        // 18位身份证号验证
        if (idNumber.length() == 18) {
            return isValid18IdNumber(idNumber);
        }

        // 15位身份证号验证（老版本）
        if (idNumber.length() == 15) {
            return isValid15IdNumber(idNumber);
        }

        return false;
    }

    /**
     * 验证身份证号格式是否正确（不允许为空）
     * 
     * @param idNumber 身份证号
     * @return 验证结果
     */
    public static boolean isValid(String idNumber) {
        return isValid(idNumber, false);
    }

    /**
     * 验证身份证号并抛出异常
     * 
     * @param idNumber 身份证号
     * @param fieldName 字段名称，用于错误信息
     * @throws IllegalArgumentException 当身份证号格式不正确时抛出
     */
    public static void validateAndThrow(String idNumber, String fieldName) {
        if (StringUtils.isEmpty(idNumber)) {
            throw new IllegalArgumentException(fieldName + "不能为空");
        }
        
        if (!isValid(idNumber)) {
            throw new IllegalArgumentException(fieldName + "格式不正确");
        }
    }

    /**
     * 验证身份证号并抛出异常（使用默认字段名）
     * 
     * @param idNumber 身份证号
     * @throws IllegalArgumentException 当身份证号格式不正确时抛出
     */
    public static void validateAndThrow(String idNumber) {
        validateAndThrow(idNumber, "身份证号");
    }

    /**
     * 验证18位身份证号
     */
    private static boolean isValid18IdNumber(String idNumber) {
        // 前17位必须是数字，最后一位可以是数字或X
        String first17 = idNumber.substring(0, 17);
        String last1 = idNumber.substring(17);

        if (!first17.matches("\\d{17}")) {
            return false;
        }

        if (!last1.matches("[0-9Xx]")) {
            return false;
        }

        // 可以在这里添加更严格的校验，如校验码验证
        return true;
    }

    /**
     * 验证15位身份证号
     */
    private static boolean isValid15IdNumber(String idNumber) {
        return idNumber.matches("\\d{15}");
    }
}
