package com.bxm.customer.domain.vo.valueAdded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 个税账号VO
 * 
 * 用于展示个税账号的详细信息，字段名称与业务表单保持一致
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("个税账号VO")
public class PersonalTaxAccountVO {

    /** 主键ID */
    @ApiModelProperty(value = "账号ID")
    private Long id;

    /** 账号（税号） */
    @ApiModelProperty(value = "账号")
    private String accountNumber;

    /** 密码 */
    @ApiModelProperty(value = "密码")
    private String password;

    /** 登录方式 */
    @ApiModelProperty(value = "登录方式")
    private String loginMethod;

    /** 实名经办人 */
    @ApiModelProperty(value = "实名经办人")
    private String realNameAgent;

    /** 手机号 */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /** 身份证号 */
    @ApiModelProperty(value = "身份证号")
    private String idNumber;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 操作方式：1-个税账号添加 */
    @ApiModelProperty(value = "操作方式：1-个税账号添加")
    private Integer operationType;

    /** 状态：1-待处理，2-已处理，3-已完成 */
    @ApiModelProperty(value = "状态：1-待处理，2-已处理，3-已完成")
    private Integer status;
}
