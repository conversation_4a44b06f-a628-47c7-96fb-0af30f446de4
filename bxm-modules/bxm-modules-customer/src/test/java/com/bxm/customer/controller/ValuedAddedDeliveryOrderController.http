### 增值交付单Controller HTTP测试文件
### 测试ValuedAddedDeliveryOrderController的upsert方法
### 基于DeliveryOrderUpsertReq参数完善的真实测试场景
###
### 字段说明：
### - valueAddedItemTypeId: 增值事项类型 (1-4)
### - taxpayerType: 纳税性质 (1-小规模纳税人, 2-一般纳税人)
### - accountingInfo: 账务类型信息 (STANDARD-标准账务, NON_STANDARD-非标账务)
### - syncHandlingFee: 是否同步手续费 (true/false)
### - syncReassignment: 是否同步改派 (true/false)
### - syncContactPerson: 是否同步联络员 (true/false)
### - syncAccountChange: 是否同步改账 (true/false)
### - modifyDueDate: 是否修改工期 (true/false)
### - taxRequirement: 税负要求 (最大200字符)
### - requirements: 交付要求 (最大100字符)
### - createUid: 创建人ID
###
### 测试用例覆盖范围：
### 1-44: 原有测试用例（基础功能、验证、边界值、业务场景）
### 45-64: 新增测试用例（新字段、格式验证、特殊字符、性能测试）

### 环境变量配置
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = application/json
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6ImRlYWMyODdkLWY5NDAtNDk0Mi1iYWUxLWNhODMzMmExOGY5NCIsInVzZXJuYW1lIjoiYWRtaW4ifQ.q0PFFjb5XL8roHik37YBgQ8Y-mGaGiFkuf3ldiOfcgG1DJLMUFEfJyYpi7r7DpyYebfnG5UQ7232NldhzkS2hQ
### ========================================
### 1. 正常新增场景测试 - 小规模纳税人 + 标准账务
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "customerName": "测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName":"个税-工资",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199001011234",
  "syncHandlingFee": false,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "请按账期提供银行流水（对账单和回单）、进销票、补账期间的个税明细报表明细的三大报表、余额表、固定资产明细表、无形资产明细表、库存表支持jpg、png、pdf、word、xls",
  "syncReassignment": false,
  "modifyDueDate": false,
  "ddl": "2025-12-31",
  "initiateDeptId": 1,
  "businessDeptId": 101,
  "businessTopDeptId": 100,
  "status": "DRAFT",
  "customerId": 1001
}

### ========================================
### 2. 正常新增场景测试 - 一般纳税人 + 非标账务
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430002C2D",
  "customerName": "一般纳税人测试公司",
  "creditCode": "91***************X",
  "taxNo": "***************",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 1,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "110000199002022345",
  "syncHandlingFee": true,
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH", "VOUCHER_BASED"]
  },
  "requirements": "社医保相关材料提交要求",
  "syncReassignment": true,
  "modifyDueDate": false,
  "ddl": "2025-06-30",
  "initiateDeptId": 2,
  "businessDeptId": 102,
  "businessTopDeptId": 100,
  "customerId": 1001
}

### ========================================
### 3. 正常更新场景测试 - 包含ID的更新操作
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "id": 1,
  "deliveryOrderNo": "VAD2508051430003E3F",
  "customerName": "更新测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 3,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199003033456",
  "syncHandlingFee": true,
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH", "SPECIAL_INDUSTRY"]
  },
  "requirements": "更新后的交付要求",
  "syncReassignment": false,
  "modifyDueDate": true,
  "ddl": "2025-11-30",
  "initiateDeptId": 1,
  "businessDeptId": 103,
  "businessTopDeptId": 100,
  "status": "DELIVERY_COMPLETED"
}

### ========================================
### 4. AccountingInfoVO验证测试 - 标准账务包含子类型（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430004G4H",
  "customerName": "账务类型验证测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "accountingInfo": {
    "mainType": "STANDARD",
    "subTypes": ["HIGH_TECH"]
  }
}

### ========================================
### 5. AccountingInfoVO验证测试 - 非标账务缺少子类型（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430005I5J",
  "customerName": "非标账务验证测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "accountingInfo": {
    "mainType": "NON_STANDARD"
  }
}

### ========================================
### 6. 参数验证测试 - 客户名称为空
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430006K6L",
  "customerName": "",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1
}

### ========================================
### 7. 参数验证测试 - 增值事项为空
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430007M7N",
  "customerName": "增值事项为空测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": null
}

### ========================================
### 8. 参数验证测试 - 增值事项字段缺失
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430008O8P",
  "customerName": "增值事项字段缺失测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1
}

### ========================================
### 9. 参数验证测试 - 信用代码格式错误
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430009Q9R",
  "customerName": "格式错误测试公司",
  "creditCode": "invalid_credit_code",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1
}

### ========================================
### 10. 参数验证测试 - 纳税性质超出范围
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430010S0T",
  "customerName": "纳税性质错误测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 5,
  "valueAddedItemTypeId": 1
}

### ========================================
### 11. 参数验证测试 - 增值事项超出范围
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430011U1V",
  "customerName": "增值事项错误测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 10
}

### ========================================
### 12. 账期验证测试 - 开始时间晚于结束时间
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430012W2X",
  "customerName": "账期验证测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "accountingPeriodStart": 202512,
  "accountingPeriodEnd": 202501
}

### ========================================
### 13. 账期验证测试 - 账期格式错误（超出范围）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430013Y3Z",
  "customerName": "账期格式错误测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "accountingPeriodStart": 199912,
  "accountingPeriodEnd": 300001
}

### ========================================
### 14. 布尔字段测试 - 所有布尔字段为true
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430014A4B",
  "customerName": "布尔字段测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "syncHandlingFee": true,
  "syncReassignment": true,
  "modifyDueDate": true,
  "accountingInfo": {
    "mainType": "STANDARD"
  }
}

### ========================================
### 15. 边界条件测试 - 最小有效数据
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430015C5D",
  "customerName": "最小数据测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1
}

### ========================================
### 16. 边界条件测试 - 最大长度字段
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430016E6F",
  "customerName": "这是一个非常长的公司名称用来测试最大长度限制这是一个非常长的公司名称用来测试最大长度限制这是一个非常长的公司名称用来测试最大长度限制这是一个非常长的公司名称用来测试最大长度限制这是一个非常长的公司名称用来测试最大长度限制",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "requirements": "这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制"
}

### ========================================
### 17. 完整业务场景测试 - 个税账号类型
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430017G7H",
  "customerName": "个税账号测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 4,
  "customerId":22,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199004044567",
  "syncHandlingFee": false,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "个税账号相关材料要求",
  "syncReassignment": true,
  "modifyDueDate": true,
  "ddl": "2025-03-31",
  "initiateDeptId": 3,
  "businessDeptId": 104,
  "businessTopDeptId": 100,
  "syncAccountChange":true,
  "syncContactPerson":true,
  "accountingDeptId":11
}

### ========================================
### 18. 测试生成交付单编号接口
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/genDeliveryOrderNo
Authorization: {{authorization}}

### ========================================
### 19. 测试根据交付单编号查询接口
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/getByOrderNo/VAD2508051430003E3F
Authorization: {{authorization}}

### ========================================
### 20. "改账"场景测试 - 正常情况
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430020M0N",
  "customerName": "改账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "改账",
  "customId": 1,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "*********01",
  "contactIdNumber": "350105199001011234",
  "syncHandlingFee": false,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "改账场景测试：需要根据客户服务ID更新相关字段信息",
  "syncReassignment": false,
  "modifyDueDate": false,
  "ddl": "2025-12-31",
  "initiateDeptId": 1,
  "businessDeptId": 105,
  "businessTopDeptId": 100,
  "status": "DRAFT"
}

### ========================================
### 21. "改账"场景测试 - customId为空
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430021O1P",
  "customerName": "改账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "改账",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "*********01"
}

### ========================================
### 22. "改账"场景测试 - customId不存在
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430022Q2R",
  "customerName": "改账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "改账",
  "customId": 99999,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "*********01"
}

### ========================================
### 23. "补账"场景测试 - 正常情况
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430023S3T",
  "customerName": "补账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "补账",
  "customerId": 1,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "*********02",
  "contactIdNumber": "350105199002022345",
  "syncHandlingFee": false,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "补账场景测试：自动补全202501-202512期间缺失的账期记录",
  "syncReassignment": false,
  "modifyDueDate": false,
  "ddl": "2025-12-31",
  "initiateDeptId": 1,
  "businessDeptId": 106,
  "businessTopDeptId": 100,
  "status": "DRAFT"
}

### ========================================
### 24. "补账"场景测试 - customerId为空
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430024U4V",
  "customerName": "补账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "补账",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "*********02"
}

### ========================================
### 25. "补账"场景测试 - 账期格式错误
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430025W5X",
  "customerName": "补账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "补账",
  "customerId": 1,
  "accountingPeriodStart": 202513,
  "accountingPeriodEnd": 202501,
  "contactMobile": "*********02"
}

### ========================================
### 26. "补账"场景测试 - customerId不存在
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430026Y6Z",
  "customerName": "补账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "补账",
  "customerId": 99999,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "*********02"
}

### ========================================
### 27. 边界值测试 - itemName为其他值（不触发特殊校验）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430027A7B",
  "customerName": "普通增值服务测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "其他服务",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "*********03"
}

### ========================================
### 28. 边界值测试 - itemName为空（不触发特殊校验）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430028C8D",
  "customerName": "无itemName测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "*********04"
}

### ========================================
### 29. 综合场景测试 - 改账+完整字段
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430029E9F",
  "customerName": "改账综合测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 3,
  "itemName": "改账",
  "customId": 1,
  "customerId": 2,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "*********05",
  "contactIdNumber": "350105199005055678",
  "syncHandlingFee": true,
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH"]
  },
  "requirements": "改账综合测试：包含所有字段的完整测试",
  "syncReassignment": true,
  "modifyDueDate": true,
  "ddl": "2025-11-30",
  "initiateDeptId": 1,
  "businessDeptId": 107,
  "businessTopDeptId": 100
}

### ========================================
### 30. 综合场景测试 - 补账+完整字段
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430030G0H",
  "customerName": "补账综合测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 4,
  "itemName": "补账",
  "customerId": 1,
  "accountingPeriodStart": 202401,
  "accountingPeriodEnd": 202412,
  "contactMobile": "*********06",
  "contactIdNumber": "350105199006066789",
  "syncHandlingFee": true,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "补账综合测试：包含所有字段的完整测试，补全2024年全年账期",
  "syncReassignment": true,
  "modifyDueDate": true,
  "ddl": "2025-10-31",
  "initiateDeptId": 2,
  "businessDeptId": 108,
  "businessTopDeptId": 100
}

### ========================================
### 31. 联系人信息验证测试 - 手机号格式验证
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430031I1J",
  "customerName": "联系人信息测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "contactMobile": "invalid_mobile",
  "contactIdNumber": "350105199001011234"
}

### ========================================
### 32. 联系人信息验证测试 - 身份证号格式验证
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430032K2L",
  "customerName": "身份证验证测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "contactMobile": "***********",
  "contactIdNumber": "invalid_id_number"
}

### ========================================
### 33. 完整的非标账务测试 - 包含所有子类型
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430033M3N",
  "customerName": "非标账务完整测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 3,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199001011234",
  "syncHandlingFee": true,
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH", "VOUCHER_BASED", "SPECIAL_INDUSTRY", "NON_PROFIT"]
  },
  "requirements": "非标账务完整测试：包含所有子类型",
  "syncReassignment": true,
  "modifyDueDate": true,
  "ddl": "2025-12-31",
  "initiateDeptId": 1,
  "businessDeptId": 109,
  "businessTopDeptId": 100
}

### ========================================
### 34. 日期边界测试 - 最早和最晚日期
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430034O4P",
  "customerName": "日期边界测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "ddl": "2030-12-31"
}

### ========================================
### 35. 税号字段测试 - 税号与信用代码不同
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430035Q5R",
  "customerName": "税号测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 2,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "accountingInfo": {
    "mainType": "STANDARD"
  }
}

### ========================================
### 36. saveStatus 方法测试 - 待交付状态保存
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/saveStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "SUBMITTED_PENDING_DELIVERY",
  "totalWithholdingAmount": 1500.00,
  "remark": "待交付状态保存测试"
}

### ========================================
### 37. saveStatus 方法测试 - 待扣款状态保存
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/saveStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430002C2D",
  "targetStatus": "CONFIRMED_PENDING_DEDUCTION",
  "totalWithholdingAmount": 2500.50,
  "remark": "待扣款状态保存测试"
}

### ========================================
### 38. saveStatus 方法测试 - 仅必填字段
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/saveStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430003E3F",
  "targetStatus": "SUBMITTED_PENDING_DELIVERY"
}

### ========================================
### 39. saveStatus 方法测试 - 交付单编号为空（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/saveStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "",
  "targetStatus": "SUBMITTED_PENDING_DELIVERY",
  "remark": "交付单编号为空测试"
}

### ========================================
### 40. saveStatus 方法测试 - 目标状态为空（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/saveStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "",
  "remark": "目标状态为空测试"
}

### ========================================
### 41. saveStatus 方法测试 - 交付单不存在（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/saveStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD9999999999999999",
  "targetStatus": "SUBMITTED_PENDING_DELIVERY",
  "remark": "交付单不存在测试"
}

### ========================================
### 42. saveStatus 方法测试 - 负数总扣缴额（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/saveStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "CONFIRMED_PENDING_DEDUCTION",
  "totalWithholdingAmount": -100.00,
  "remark": "负数总扣缴额测试"
}

### ========================================
### 43. saveStatus 方法测试 - 超长备注（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/saveStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "SUBMITTED_PENDING_DELIVERY",
  "remark": "这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制这是一个超长的备注信息用来测试最大长度限制"
}

### ========================================
### 44. saveStatus 方法测试 - 零总扣缴额（边界值测试）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/saveStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "CONFIRMED_PENDING_DEDUCTION",
  "totalWithholdingAmount": 0.00,
  "remark": "零总扣缴额边界值测试"
}

### ========================================
### 45. 新字段完整性测试 - 包含所有新增字段
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430045A5B",
  "customerName": "新字段完整性测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 3,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199001011234",
  "syncHandlingFee": true,
  "syncReassignment": true,
  "syncContactPerson": true,
  "syncAccountChange": true,
  "modifyDueDate": true,
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH", "VOUCHER_BASED"]
  },
  "requirements": "包含所有新字段的完整测试用例，验证系统对新增字段的处理能力",
  "taxRequirement": "税负要求测试：增值税税负率不超过3%，企业所得税税负率控制在合理范围内，个税申报及时准确",
  "ddl": "2025-12-31",
  "initiateDeptId": 1,
  "businessDeptId": 110,
  "businessTopDeptId": 100,
  "createUid": 1001,
  "customerId": 1001
}

### ========================================
### 46. taxRequirement字段长度边界测试 - 最大长度200字符
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430046C6D",
  "customerName": "税负要求长度测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "taxRequirement": "这是一个测试税负要求字段最大长度的用例这是一个测试税负要求字段最大长度的用例这是一个测试税负要求字段最大长度的用例这是一个测试税负要求字段最大长度的用例这是一个测试税负要求字段最大长度的用例这是一个测试税负要求字段最大长度的用例这是一个测试税负要求字段最大长度的用例这是一个测试税负要求字段最大长度的用例"
}

### ========================================
### 47. taxRequirement字段长度超限测试 - 超过200字符（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430047E7F",
  "customerName": "税负要求超长测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "taxRequirement": "这是一个测试税负要求字段超过最大长度限制的用例这是一个测试税负要求字段超过最大长度限制的用例这是一个测试税负要求字段超过最大长度限制的用例这是一个测试税负要求字段超过最大长度限制的用例这是一个测试税负要求字段超过最大长度限制的用例这是一个测试税负要求字段超过最大长度限制的用例这是一个测试税负要求字段超过最大长度限制的用例这是一个测试税负要求字段超过最大长度限制的用例这是一个测试税负要求字段超过最大长度限制的用例这是一个测试税负要求字段超过最大长度限制的用例"
}

### ========================================
### 48. requirements字段长度超限测试 - 超过100字符（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430048G8H",
  "customerName": "交付要求超长测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "requirements": "这是一个测试交付要求字段超过最大长度限制的用例这是一个测试交付要求字段超过最大长度限制的用例这是一个测试交付要求字段超过最大长度限制的用例这是一个测试交付要求字段超过最大长度限制的用例"
}

### ========================================
### 49. 手机号格式验证测试 - 标准11位手机号
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430049I9J",
  "customerName": "手机号格式测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "contactMobile": "18612345678",
  "contactIdNumber": "350105199001011234"
}

### ========================================
### 50. 手机号格式验证测试 - 非标准格式（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430050K0L",
  "customerName": "手机号格式错误测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "contactMobile": "12345",
  "contactIdNumber": "350105199001011234"
}

### ========================================
### 51. 身份证号格式验证测试 - 18位标准格式
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430051M1N",
  "customerName": "身份证号格式测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "contactMobile": "***********",
  "contactIdNumber": "35010519900101123X"
}

### ========================================
### 52. 身份证号格式验证测试 - 15位旧格式
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430052O2P",
  "customerName": "身份证号旧格式测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "contactMobile": "***********",
  "contactIdNumber": "350105900101123"
}

### ========================================
### 53. 布尔字段组合测试 - 所有同步字段为true
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430053Q3R",
  "customerName": "布尔字段组合测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 2,
  "syncHandlingFee": true,
  "syncReassignment": true,
  "syncContactPerson": true,
  "syncAccountChange": true,
  "modifyDueDate": true,
  "accountingInfo": {
    "mainType": "STANDARD"
  }
}

### ========================================
### 54. 布尔字段组合测试 - 所有同步字段为false
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430054S4T",
  "customerName": "布尔字段false组合测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 3,
  "syncHandlingFee": false,
  "syncReassignment": false,
  "syncContactPerson": false,
  "syncAccountChange": false,
  "modifyDueDate": false,
  "accountingInfo": {
    "mainType": "STANDARD"
  }
}

### ========================================
### 55. 特殊字符测试 - 客户名称包含特殊字符
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430055U5V",
  "customerName": "测试企业（有限公司）&Co.,Ltd.@#￥%",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "requirements": "包含特殊字符的交付要求：@#￥%…&*()_+-=[]{}|;':\",./<>?",
  "taxRequirement": "特殊字符税负要求：增值税≤3%，企业所得税≥1%"
}

### ========================================
### 56. Unicode字符测试 - 包含emoji和多语言字符
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430056W6X",
  "customerName": "测试企业🏢有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "requirements": "多语言测试：English中文日本語한국어Русский",
  "taxRequirement": "包含emoji的税负要求：📊税负率控制在合理范围✅"
}

### ========================================
### 57. 空字符串与null值测试 - 可选字段为空字符串
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430057Y7Z",
  "customerName": "空字符串测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "taxNo": "",
  "contactMobile": "",
  "contactIdNumber": "",
  "requirements": "",
  "taxRequirement": ""
}

### ========================================
### 58. 数值边界测试 - 账期边界值
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430058A8B",
  "customerName": "账期边界值测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "accountingPeriodStart": 200001,
  "accountingPeriodEnd": 299912
}

### ========================================
### 59. 数值边界测试 - 账期超出边界（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430059C9D",
  "customerName": "账期超界测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "accountingPeriodStart": 199912,
  "accountingPeriodEnd": 300001
}

### ========================================
### 60. JSON格式错误测试 - 缺少必要的逗号（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430060E0F"
  "customerName": "JSON格式错误测试企业"
  "creditCode": "91350105MA2Y9DXW8L"
  "taxpayerType": 1
  "valueAddedItemTypeId": 1
}

### ========================================
### 61. 完整业务场景测试 - 包含所有可选字段的最大数据集
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430061G1H",
  "title": "完整业务场景测试交付单",
  "customerId": 1001,
  "customerName": "完整业务场景测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 4,
  "itemName": "完整业务测试",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199001011234",
  "syncHandlingFee": true,
  "syncReassignment": true,
  "syncContactPerson": true,
  "syncAccountChange": true,
  "modifyDueDate": true,
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH", "VOUCHER_BASED", "SPECIAL_INDUSTRY"]
  },
  "requirements": "完整业务场景的交付要求，包含所有必要的材料和流程说明",
  "taxRequirement": "完整的税负要求：增值税税负率控制在3%以内，企业所得税税负率保持在行业平均水平，个税申报准确及时",
  "ddl": "2025-12-31",
  "initiateDeptId": 1,
  "businessDeptId": 111,
  "businessTopDeptId": 100,
  "status": "DRAFT",
  "createUid": 1001
}

### ========================================
### 62. 性能测试 - 大量数据字段测试
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430062I2J",
  "customerName": "性能测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "requirements": "性能测试用例：测试系统在处理包含大量字符的字段时的性能表现，确保系统能够稳定处理正常业务范围内的数据量",
  "taxRequirement": "性能测试的税负要求：在大数据量处理时仍需保证税负计算的准确性，增值税税负率控制在合理范围内，确保系统响应时间在可接受范围"
}

### ========================================
### 63. 边界组合测试 - 最小必填字段 + 最大可选字段
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430063K3L",
  "customerName": "边界组合测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "requirements": "边界组合测试：最小必填字段配合最大长度的可选字段，验证系统的边界处理能力和数据一致性",
  "taxRequirement": "边界组合测试的税负要求：在最小配置下仍需确保税负计算的准确性，增值税税负率控制标准不变，企业所得税申报要求保持一致性验证"
}

### ========================================
### 64. 最终综合测试 - 真实业务场景模拟
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430064M4N",
  "title": "2025年度增值税专项服务交付单",
  "customerId": 1001,
  "customerName": "福建省示范科技有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 3,
  "itemName": "增值税专项服务",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105198801011234",
  "syncHandlingFee": true,
  "syncReassignment": false,
  "syncContactPerson": true,
  "syncAccountChange": false,
  "modifyDueDate": true,
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH", "SPECIAL_INDUSTRY"]
  },
  "requirements": "提供2025年全年增值税申报材料，包括进销项发票、银行流水、财务报表等",
  "taxRequirement": "增值税税负率控制在2.5%以内，确保享受高新技术企业优惠政策",
  "ddl": "2025-12-31",
  "initiateDeptId": 1,
  "businessDeptId": 112,
  "businessTopDeptId": 100,
  "status": "DRAFT",
  "createUid": 1001
}
